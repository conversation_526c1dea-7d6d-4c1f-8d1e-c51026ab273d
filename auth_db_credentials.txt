Supabase 数据库+认证 部署凭据
================================
PostgreSQL 密码: 413892d85a0f35e113f0babb6d52c05d
Dashboard 密码: 49cc004a5aa3dbec
JWT 密钥: af5e8a19f951cf5addbd43675616bb1d201fda47be107ad19b55fef249e64466

访问信息:
- PostgreSQL: localhost:5432
- REST API: http://localhost:3001
- Auth API: http://localhost:9999
- API Gateway: http://localhost:8000
- Meta API: http://localhost:8080

数据库连接:
- 主机: localhost
- 端口: 5432
- 数据库: postgres
- 用户: postgres
- 密码: 413892d85a0f35e113f0babb6d52c05d

API 密钥:
- Anon Key: 在环境文件中查看
- Service Role Key: 在环境文件中查看

请妥善保存这些凭据！

# Supabase 模块化管理解决方案

## 🎯 概述

本解决方案提供了灵活的 Supabase 模块管理功能，允许您选择性地启用所需的功能模块，优化资源使用。

## 📁 文件结构

```
├── supabase_manager.sh          # 主要的模块管理脚本
├── supabase_modules.conf        # 模块配置文件
├── deploy_auth_db.sh            # 数据库+认证快速部署脚本
├── docker-compose.auth-db.yml   # 精简的 Docker Compose 配置
├── .env.auth-db                 # 精简环境配置
└── README_模块化管理.md         # 本文档
```

## 🧩 Supabase 模块分析

### 核心模块 (必需)
- **db** - PostgreSQL 数据库核心 (高资源消耗)
- **vector** - 日志收集和路由 (低资源消耗)

### 网络和API
- **kong** - API网关和路由 (中等资源消耗)
- **rest** - REST API服务 (中等资源消耗)
- **meta** - 数据库元数据管理 (低资源消耗)

### 认证和授权
- **auth** - 用户认证和授权服务 (中等资源消耗)

### 实时和存储
- **realtime** - 实时数据同步 (中等资源消耗)
- **storage** - 文件存储服务 (中等资源消耗)
- **imgproxy** - 图片处理服务 (低资源消耗)

### 管理和高级功能
- **studio** - Web管理界面 (中等资源消耗)
- **functions** - Edge Functions运行时 (中等资源消耗)
- **analytics** - 日志分析和监控 (中等资源消耗)
- **supavisor** - 数据库连接池 (低资源消耗)

## 🎬 预定义使用场景

### 1. 最小化部署 (SCENARIO_MINIMAL)
**模块**: db, vector  
**资源**: ~200MB RAM  
**用途**: 仅数据库功能

### 2. 仅数据库服务 (SCENARIO_DATABASE_ONLY)
**模块**: db, vector, meta, supavisor  
**资源**: ~250MB RAM  
**用途**: 数据库 + 管理工具

### 3. 数据库+认证 (SCENARIO_AUTH_DB) ⭐ **推荐给您**
**模块**: db, vector, kong, rest, auth, analytics, meta  
**资源**: ~400MB RAM  
**用途**: 数据库 + 用户登录认证

### 4. 基础API服务 (SCENARIO_API_BASIC)
**模块**: db, vector, kong, rest, auth, analytics  
**资源**: ~450MB RAM  
**用途**: 完整的后端API

### 5. 完整后端 (SCENARIO_FULL_BACKEND)
**模块**: 除 studio 外的所有模块  
**资源**: ~800MB RAM  
**用途**: 生产环境后端

### 6. 完整开发环境 (SCENARIO_DEVELOPMENT)
**模块**: 所有模块  
**资源**: ~1GB RAM  
**用途**: 开发和测试

## 🚀 快速开始 (推荐)

### 针对您的需求 (数据库 + 用户登录)

```bash
# 1. 给脚本添加执行权限
chmod +x deploy_auth_db.sh

# 2. 运行快速部署
./deploy_auth_db.sh
```

这将部署包含以下功能的精简版本:
- ✅ PostgreSQL 数据库
- ✅ 用户认证和登录
- ✅ REST API 访问
- ✅ API 网关

## 🛠️ 高级使用

### 使用模块管理器

```bash
# 给脚本添加执行权限
chmod +x supabase_manager.sh

# 显示可用模块
./supabase_manager.sh --modules

# 显示预定义场景
./supabase_manager.sh --scenarios

# 部署数据库+认证场景
./supabase_manager.sh --scenario SCENARIO_AUTH_DB

# 交互式选择模块
./supabase_manager.sh --interactive

# 部署指定模块
./supabase_manager.sh --deploy db,auth,rest

# 查看当前状态
./supabase_manager.sh --status

# 停止所有服务
./supabase_manager.sh --stop
```

### 自定义模块组合

```bash
# 仅数据库
./supabase_manager.sh --deploy db,vector

# 数据库 + API
./supabase_manager.sh --deploy db,vector,rest,kong

# 数据库 + 认证 + API
./supabase_manager.sh --deploy db,vector,auth,rest,kong
```

## 📊 资源使用对比

| 场景 | 容器数量 | 预估内存 | 启动时间 | 适用场景 |
|------|----------|----------|----------|----------|
| 最小化 | 2 | ~200MB | 30s | 仅数据库 |
| 数据库+认证 | 6 | ~400MB | 60s | **您的需求** |
| 基础API | 5 | ~450MB | 60s | 简单后端 |
| 完整后端 | 11 | ~800MB | 90s | 生产环境 |
| 完整开发 | 13 | ~1GB | 120s | 开发测试 |

## 🔧 配置说明

### 环境变量配置

主要配置文件: `.env.auth-db`

```bash
# 数据库配置
POSTGRES_PASSWORD=your-secure-password
POSTGRES_PORT=5432

# JWT 配置
JWT_SECRET=your-jwt-secret
JWT_EXPIRY=3600

# API 配置
API_EXTERNAL_URL=http://localhost:8000
SUPABASE_PUBLIC_URL=http://localhost:8000

# 认证配置
SITE_URL=http://localhost:3000
DISABLE_SIGNUP=false
ENABLE_EMAIL_SIGNUP=true
```

### 端口映射

| 服务 | 端口 | 说明 |
|------|------|------|
| PostgreSQL | 5432 | 数据库连接 |
| REST API | 3001 | 直接API访问 |
| Auth API | 9999 | 认证服务 |
| API Gateway | 8000 | 统一API入口 |
| Meta API | 8080 | 数据库管理 |

## 🔐 安全配置

### 生产环境建议

1. **修改默认密码**
```bash
# 编辑 .env.auth-db 文件
POSTGRES_PASSWORD=your-very-secure-password
JWT_SECRET=your-very-long-jwt-secret-at-least-32-characters
DASHBOARD_PASSWORD=your-secure-dashboard-password
```

2. **限制注册**
```bash
DISABLE_SIGNUP=true  # 禁用公开注册
```

3. **配置邮件服务**
```bash
SMTP_HOST=your-smtp-server
SMTP_USER=your-email
SMTP_PASS=your-email-password
```

## 🧪 测试连接

### 数据库连接测试
```bash
# 使用 psql 连接
psql -h localhost -p 5432 -U postgres -d postgres

# 使用凭据文件中的密码
```

### API 测试
```bash
# 测试认证API
curl http://localhost:9999/health

# 测试REST API
curl -H "apikey: YOUR_ANON_KEY" http://localhost:8000/rest/v1/

# 测试网关
curl http://localhost:8000/health
```

## 🔄 管理操作

### 启动/停止服务

```bash
# 启动 (快速部署版本)
cd /path/to/scripts
docker-compose -f docker-compose.auth-db.yml up -d

# 停止
docker-compose -f docker-compose.auth-db.yml down

# 重启
docker-compose -f docker-compose.auth-db.yml restart

# 查看日志
docker-compose -f docker-compose.auth-db.yml logs

# 查看状态
docker-compose -f docker-compose.auth-db.yml ps
```

### 数据备份

```bash
# 备份数据库
docker exec supabase-db pg_dump -U postgres postgres > backup.sql

# 恢复数据库
docker exec -i supabase-db psql -U postgres postgres < backup.sql
```

## 🆘 故障排除

### 常见问题

1. **端口冲突**
   - 修改 `.env.auth-db` 中的端口配置
   - 检查端口占用: `netstat -tlnp | grep :5432`

2. **内存不足**
   - 使用更精简的配置
   - 增加系统内存或使用交换空间

3. **服务启动失败**
   - 查看日志: `docker-compose logs`
   - 检查依赖服务是否正常

4. **认证失败**
   - 检查 JWT_SECRET 配置
   - 验证 API 密钥设置

### 日志查看

```bash
# 查看所有服务日志
docker-compose -f docker-compose.auth-db.yml logs

# 查看特定服务日志
docker-compose -f docker-compose.auth-db.yml logs auth
docker-compose -f docker-compose.auth-db.yml logs db
```

## 📞 支持

如果遇到问题，请提供以下信息:
- 使用的配置场景
- 错误日志输出
- 系统资源情况
- Docker 版本信息

---

**推荐**: 对于您的需求 (数据库 + 用户登录)，建议使用 `deploy_auth_db.sh` 快速部署脚本，它提供了最优化的配置。

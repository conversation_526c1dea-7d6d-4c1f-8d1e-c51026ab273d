#!/bin/bash

# Supabase Studio 问题诊断和修复脚本
# 解决 Studio 中的 "Failed to retrieve tables" 等错误
# 作者: Claude

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

# 日志函数
log_info() { echo -e "${GREEN}[INFO]${NC} $1"; }
log_warn() { echo -e "${YELLOW}[WARN]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }
log_step() { echo -e "${BLUE}[STEP]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }

# 配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
SUPABASE_DIR="$HOME/supabase/docker"

# 检查必需的服务
check_required_services() {
    log_step "检查 Studio 必需的服务..."
    
    local required_services=("supabase-studio" "supabase-kong" "supabase-rest" "supabase-meta" "supabase-db")
    local missing_services=()
    
    for service in "${required_services[@]}"; do
        if ! docker ps --format "{{.Names}}" | grep -q "^$service$"; then
            missing_services+=("$service")
        fi
    done
    
    if [[ ${#missing_services[@]} -eq 0 ]]; then
        log_success "所有必需服务都在运行 ✓"
        return 0
    else
        log_error "缺少以下服务: ${missing_services[*]}"
        return 1
    fi
}

# 检查服务健康状态
check_service_health() {
    log_step "检查服务健康状态..."
    
    echo "服务状态详情:"
    docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | grep supabase
    
    echo ""
    echo "检查各服务健康状态:"
    
    # 检查数据库
    if docker exec supabase-db pg_isready -U postgres &>/dev/null; then
        log_success "数据库健康 ✓"
    else
        log_error "数据库不健康"
    fi
    
    # 检查 REST API
    if curl -s http://localhost:8000/rest/v1/ -H "apikey: $(grep ANON_KEY $SUPABASE_DIR/.env | cut -d'=' -f2)" &>/dev/null; then
        log_success "REST API 健康 ✓"
    else
        log_error "REST API 不健康"
    fi
    
    # 检查 Meta API
    if curl -s http://localhost:8080 &>/dev/null; then
        log_success "Meta API 健康 ✓"
    else
        log_error "Meta API 不健康"
    fi
    
    # 检查 Kong 网关
    if curl -s http://localhost:8000 &>/dev/null; then
        log_success "Kong 网关健康 ✓"
    else
        log_error "Kong 网关不健康"
    fi
}

# 检查网络连接
check_network_connectivity() {
    log_step "检查容器网络连接..."
    
    # 检查 Studio 是否能连接到其他服务
    echo "检查 Studio 容器网络连接:"
    
    # 检查到 Kong 的连接
    if docker exec supabase-studio wget -q --spider http://kong:8000 2>/dev/null; then
        log_success "Studio -> Kong 连接正常 ✓"
    else
        log_error "Studio -> Kong 连接失败"
    fi
    
    # 检查到 Meta 的连接
    if docker exec supabase-studio wget -q --spider http://meta:8080 2>/dev/null; then
        log_success "Studio -> Meta 连接正常 ✓"
    else
        log_error "Studio -> Meta 连接失败"
    fi
    
    # 检查到数据库的连接
    if docker exec supabase-studio nc -z db 5432 2>/dev/null; then
        log_success "Studio -> 数据库 连接正常 ✓"
    else
        log_error "Studio -> 数据库 连接失败"
    fi
}

# 检查环境变量配置
check_environment_config() {
    log_step "检查环境变量配置..."
    
    if [[ ! -f "$SUPABASE_DIR/.env" ]]; then
        log_error "环境文件不存在: $SUPABASE_DIR/.env"
        return 1
    fi
    
    # 检查关键环境变量
    local required_vars=("ANON_KEY" "SERVICE_ROLE_KEY" "JWT_SECRET" "POSTGRES_PASSWORD")
    local missing_vars=()
    
    for var in "${required_vars[@]}"; do
        if ! grep -q "^$var=" "$SUPABASE_DIR/.env"; then
            missing_vars+=("$var")
        fi
    done
    
    if [[ ${#missing_vars[@]} -eq 0 ]]; then
        log_success "所有必需环境变量都已配置 ✓"
    else
        log_error "缺少环境变量: ${missing_vars[*]}"
    fi
    
    # 显示关键配置
    echo ""
    echo "关键配置信息:"
    echo "API URL: http://localhost:8000"
    echo "Studio URL: http://localhost:3000"
    echo "Database URL: postgresql://postgres:***@localhost:5432/postgres"
}

# 检查 Studio 日志
check_studio_logs() {
    log_step "检查 Studio 容器日志..."
    
    echo "最近的 Studio 日志 (最后20行):"
    docker logs --tail 20 supabase-studio
    
    echo ""
    echo "检查是否有错误信息:"
    if docker logs supabase-studio 2>&1 | grep -i error | tail -5; then
        log_warn "发现错误信息，请查看上述日志"
    else
        log_info "未发现明显错误信息"
    fi
}

# 重启相关服务
restart_services() {
    log_step "重启相关服务..."
    
    echo "重启顺序: Studio -> Kong -> Meta"
    
    # 重启 Studio
    log_info "重启 Studio..."
    docker restart supabase-studio
    sleep 5
    
    # 重启 Kong
    log_info "重启 Kong..."
    docker restart supabase-kong
    sleep 5
    
    # 重启 Meta
    log_info "重启 Meta..."
    docker restart supabase-meta
    sleep 5
    
    log_success "服务重启完成"
}

# 验证修复结果
verify_fix() {
    log_step "验证修复结果..."
    
    echo "等待服务完全启动..."
    sleep 15
    
    # 检查服务状态
    echo "检查服务状态:"
    docker ps --format "table {{.Names}}\t{{.Status}}" | grep supabase
    
    echo ""
    echo "测试 API 连接:"
    
    # 测试 Studio
    if curl -s http://localhost:3000 | grep -q "supabase"; then
        log_success "Studio 可访问 ✓"
    else
        log_error "Studio 仍无法访问"
    fi
    
    # 测试 REST API
    if curl -s http://localhost:8000/rest/v1/ -H "apikey: $(grep ANON_KEY $SUPABASE_DIR/.env | cut -d'=' -f2)" | grep -q "swagger"; then
        log_success "REST API 可访问 ✓"
    else
        log_error "REST API 仍无法访问"
    fi
    
    # 测试 Meta API
    if curl -s http://localhost:8080 &>/dev/null; then
        log_success "Meta API 可访问 ✓"
    else
        log_error "Meta API 仍无法访问"
    fi
}

# 提供解决方案建议
provide_solutions() {
    log_step "提供解决方案建议..."
    
    echo ""
    echo "=========================================="
    echo "           Studio 问题解决方案"
    echo "=========================================="
    echo ""
    
    echo "问题: Failed to retrieve tables / Failed to load schemas"
    echo ""
    echo "原因分析:"
    echo "1. Studio 需要连接到 Meta API 来获取数据库架构信息"
    echo "2. Studio 需要通过 Kong 网关访问 REST API"
    echo "3. 缺少必需的服务会导致连接失败"
    echo ""
    
    echo "解决方案:"
    echo ""
    echo "方案1: 启动完整的 Studio 依赖服务 (推荐)"
    echo "  ./supabase_manager.sh --deploy studio,kong,rest,meta,auth"
    echo ""
    
    echo "方案2: 使用预定义场景"
    echo "  ./supabase_manager.sh --scenario SCENARIO_DEVELOPMENT"
    echo ""
    
    echo "方案3: 手动重启服务"
    echo "  docker restart supabase-studio supabase-kong supabase-meta"
    echo ""
    
    echo "方案4: 检查和修复网络问题"
    echo "  docker network ls"
    echo "  docker network inspect supabase_default"
    echo ""
    
    echo "=========================================="
    echo "           预防措施"
    echo "=========================================="
    echo ""
    echo "1. 始终使用完整的服务组合启动 Studio:"
    echo "   - studio (Web界面)"
    echo "   - kong (API网关)"
    echo "   - rest (REST API)"
    echo "   - meta (数据库元数据)"
    echo "   - db (数据库)"
    echo "   - auth (认证服务，可选)"
    echo ""
    
    echo "2. 使用模块管理器的预定义场景:"
    echo "   ./supabase_manager.sh --scenarios"
    echo ""
    
    echo "3. 定期检查服务健康状态:"
    echo "   ./supabase_manager.sh --status"
    echo ""
}

# 主函数
main() {
    echo "=========================================="
    echo "    Supabase Studio 问题诊断工具"
    echo "=========================================="
    echo ""
    
    # 检查必需服务
    if ! check_required_services; then
        echo ""
        log_warn "检测到缺少必需服务，正在启动..."
        
        if [[ -f "$SCRIPT_DIR/supabase_manager.sh" ]]; then
            log_info "使用模块管理器启动完整的 Studio 服务..."
            "$SCRIPT_DIR/supabase_manager.sh" --deploy studio,kong,rest,meta,auth,analytics
            
            echo ""
            log_info "等待服务启动..."
            sleep 20
            
            # 重新检查
            if check_required_services; then
                log_success "服务启动成功！"
            else
                log_error "服务启动失败，请手动检查"
                exit 1
            fi
        else
            log_error "找不到模块管理器，请手动启动服务"
            provide_solutions
            exit 1
        fi
    fi
    
    echo ""
    check_service_health
    
    echo ""
    check_network_connectivity
    
    echo ""
    check_environment_config
    
    echo ""
    check_studio_logs
    
    echo ""
    verify_fix
    
    echo ""
    echo "=========================================="
    echo "           诊断完成"
    echo "=========================================="
    echo ""
    
    log_info "Studio 诊断完成！"
    log_info "如果问题仍然存在，请查看上述日志和建议"
    
    echo ""
    echo "快速访问链接:"
    echo "- Supabase Studio: http://localhost:3000"
    echo "- API 文档: http://localhost:8000/rest/v1/"
    echo "- 数据库管理: http://localhost:8080"
    
    provide_solutions
}

# 处理命令行参数
case "${1:-}" in
    --restart)
        restart_services
        verify_fix
        ;;
    --logs)
        check_studio_logs
        ;;
    --health)
        check_service_health
        ;;
    --network)
        check_network_connectivity
        ;;
    --help|-h)
        echo "Supabase Studio 问题诊断工具"
        echo ""
        echo "用法: $0 [选项]"
        echo ""
        echo "选项:"
        echo "  --restart    重启相关服务"
        echo "  --logs       查看 Studio 日志"
        echo "  --health     检查服务健康状态"
        echo "  --network    检查网络连接"
        echo "  --help, -h   显示此帮助信息"
        echo ""
        echo "不带参数运行将执行完整诊断"
        ;;
    *)
        main
        ;;
esac

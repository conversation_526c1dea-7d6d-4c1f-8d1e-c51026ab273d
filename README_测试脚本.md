# Supabase 测试脚本使用指南

本目录包含用于测试和诊断 Supabase 实例的脚本工具。

## 脚本文件

### 1. `test_supabase.sh` - 完整测试脚本
全面的 Supabase 实例测试工具，提供详细的功能验证和诊断。

**功能特性:**
- ✅ Docker 服务状态检查
- ✅ 端口连通性测试 (3000, 8000, 5432, 4000)
- ✅ HTTP 端点响应测试
- ✅ PostgreSQL 数据库连接测试
- ✅ REST/Auth/Storage API 测试
- ✅ 实时功能 (WebSocket) 测试
- ✅ 端口映射问题诊断
- ✅ 交互式菜单选择
- ✅ 详细测试报告生成

### 2. `quick_test.sh` - 快速检查脚本
轻量级的快速状态检查工具，用于基本的健康检查。

**功能特性:**
- ⚡ 快速服务状态检查
- ⚡ 基本端口连通性测试
- ⚡ 简化的诊断信息
- ⚡ 常用命令提示

## 使用方法

### 快速检查 (推荐首次使用)
```bash
./quick_test.sh
```

### 完整测试 - 交互式模式
```bash
./test_supabase.sh
```
然后根据菜单选择要执行的测试项目。

### 完整测试 - 命令行模式
```bash
# 执行所有测试
./test_supabase.sh --full

# 仅测试 Docker 服务
./test_supabase.sh --docker

# 仅测试端口连通性
./test_supabase.sh --ports

# 仅测试 HTTP 端点
./test_supabase.sh --http

# 仅测试数据库连接
./test_supabase.sh --database

# 仅测试 API 端点
./test_supabase.sh --api

# 仅测试实时功能
./test_supabase.sh --realtime

# 诊断端口映射问题
./test_supabase.sh --diagnose

# 显示帮助信息
./test_supabase.sh --help
```

## 常见问题解决

### 问题1: 端口3000无法访问
**症状:** 无法通过 http://localhost:3000 访问 Supabase Studio

**解决方案:**
1. 检查容器状态:
   ```bash
   cd ~/supabase/docker
   docker-compose ps
   ```

2. 检查端口映射:
   ```bash
   docker ps --format "table {{.Names}}\t{{.Ports}}" | grep 3000
   ```

3. 重启服务:
   ```bash
   cd ~/supabase/docker
   docker-compose restart
   ```

4. 检查防火墙:
   ```bash
   sudo ufw allow 3000
   ```

### 问题2: 数据库连接失败
**症状:** PostgreSQL 连接测试失败

**解决方案:**
1. 安装 PostgreSQL 客户端:
   ```bash
   sudo apt install postgresql-client
   ```

2. 检查数据库容器:
   ```bash
   cd ~/supabase/docker
   docker-compose logs db
   ```

3. 验证凭据:
   ```bash
   cat ~/supabase/docker/supabase_credentials.txt
   ```

### 问题3: API端点无响应
**症状:** REST/Auth/Storage API 测试失败

**解决方案:**
1. 检查 Kong 网关容器:
   ```bash
   cd ~/supabase/docker
   docker-compose logs kong
   ```

2. 重启 API 网关:
   ```bash
   cd ~/supabase/docker
   docker-compose restart kong
   ```

3. 检查 API 密钥配置:
   ```bash
   grep "ANON_KEY" ~/supabase/docker/.env
   ```

## 测试依赖

### 必需依赖
- `docker` - Docker 容器管理
- `docker-compose` - 多容器应用管理
- `curl` - HTTP 请求测试

### 可选依赖 (用于增强功能)
- `psql` - PostgreSQL 数据库连接测试
- `wscat` - WebSocket 连接测试
- `netstat` 或 `ss` - 端口监听状态检查

### 安装可选依赖
```bash
# 安装 PostgreSQL 客户端
sudo apt install postgresql-client

# 安装 WebSocket 测试工具
sudo npm install -g wscat

# 安装网络工具 (通常已预装)
sudo apt install net-tools
```

## 测试报告解读

### 测试状态
- ✅ `[PASS]` - 测试通过
- ❌ `[FAIL]` - 测试失败
- ⚠️ `[WARN]` - 警告信息

### 关键指标
- **总测试数** - 执行的测试项目总数
- **通过测试** - 成功通过的测试数量
- **失败测试** - 失败的测试数量

### 服务端口说明
- **3000** - Supabase Studio (Web管理界面)
- **8000** - API Gateway (REST/Auth/Storage API)
- **5432** - PostgreSQL 数据库
- **4000** - Realtime 服务 (WebSocket)

## 故障排除流程

1. **首先运行快速检查:**
   ```bash
   ./quick_test.sh
   ```

2. **如有问题，运行完整诊断:**
   ```bash
   ./test_supabase.sh --diagnose
   ```

3. **查看详细日志:**
   ```bash
   cd ~/supabase/docker
   docker-compose logs
   ```

4. **重启服务:**
   ```bash
   cd ~/supabase/docker
   docker-compose restart
   ```

5. **如问题持续，重新部署:**
   ```bash
   cd ~/supabase/docker
   docker-compose down
   docker-compose up -d
   ```

## 联系支持

如果测试脚本发现问题但无法解决，请提供以下信息：
- 测试脚本的完整输出
- `docker-compose logs` 的输出
- 系统信息 (`uname -a`, `docker --version`)

---

**注意:** 这些脚本设计用于测试通过 `deploy_supabase.sh` 部署的 Supabase 实例。如果您使用其他方式部署，可能需要调整配置参数。

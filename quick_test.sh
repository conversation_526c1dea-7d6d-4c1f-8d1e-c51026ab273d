#!/bin/bash

# Supabase 快速测试脚本
# 用于快速检查 Supabase 实例的基本功能
# 作者: Claude

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 配置
SUPABASE_DIR="$HOME/supabase/docker"
STUDIO_URL="http://localhost:3000"
API_URL="http://localhost:8000"

# 日志函数
log_info() { echo -e "${GREEN}[INFO]${NC} $1"; }
log_warn() { echo -e "${YELLOW}[WARN]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }
log_test() { echo -e "${BLUE}[TEST]${NC} $1"; }

# 检查端口
check_port() {
    timeout 3 bash -c "echo >/dev/tcp/$1/$2" 2>/dev/null
}

# 检查HTTP
check_http() {
    curl -s -o /dev/null -w "%{http_code}" --connect-timeout 5 "$1" 2>/dev/null
}

echo "=========================================="
echo "      Supabase 快速状态检查"
echo "=========================================="

# 1. 检查Docker服务
log_test "检查Docker服务..."
if systemctl is-active --quiet docker; then
    echo "✅ Docker服务运行正常"
else
    echo "❌ Docker服务未运行"
    exit 1
fi

# 2. 检查Supabase目录
log_test "检查Supabase安装..."
if [[ -d "$SUPABASE_DIR" ]]; then
    echo "✅ Supabase目录存在"
else
    echo "❌ Supabase目录不存在: $SUPABASE_DIR"
    exit 1
fi

# 3. 检查Docker Compose服务
log_test "检查容器状态..."
cd "$SUPABASE_DIR"
if docker-compose ps | grep -q "Up"; then
    echo "✅ Docker Compose服务运行中"
    running_services=$(docker-compose ps --services --filter "status=running" | wc -l)
    total_services=$(docker-compose ps --services | wc -l)
    echo "   运行中的服务: $running_services/$total_services"
else
    echo "❌ Docker Compose服务未运行"
    echo "尝试启动服务..."
    docker-compose up -d
    sleep 10
fi

# 4. 检查关键端口
log_test "检查端口连通性..."

# Supabase Studio (3000)
if check_port localhost 3000; then
    echo "✅ 端口3000 (Supabase Studio) 可访问"
    
    # 检查HTTP响应
    http_code=$(check_http "$STUDIO_URL")
    if [[ "$http_code" =~ ^[23][0-9][0-9]$ ]]; then
        echo "✅ Supabase Studio HTTP响应正常 (状态码: $http_code)"
    else
        echo "⚠️  Supabase Studio HTTP响应异常 (状态码: $http_code)"
    fi
else
    echo "❌ 端口3000 (Supabase Studio) 无法访问"
    
    # 诊断信息
    echo "🔍 诊断信息:"
    echo "   检查容器端口映射:"
    docker ps --format "table {{.Names}}\t{{.Ports}}" | grep 3000 || echo "   没有容器映射到端口3000"
    
    if command -v netstat &>/dev/null; then
        echo "   检查端口监听:"
        netstat -tlnp | grep :3000 || echo "   端口3000未被监听"
    fi
fi

# API Gateway (8000)
if check_port localhost 8000; then
    echo "✅ 端口8000 (API Gateway) 可访问"
else
    echo "❌ 端口8000 (API Gateway) 无法访问"
fi

# PostgreSQL (5432)
if check_port localhost 5432; then
    echo "✅ 端口5432 (PostgreSQL) 可访问"
else
    echo "❌ 端口5432 (PostgreSQL) 无法访问"
fi

# 5. 显示访问信息
echo ""
echo "=========================================="
echo "           访问信息"
echo "=========================================="
echo "Supabase Studio: $STUDIO_URL"
echo "API Gateway:     $API_URL"
echo "PostgreSQL:      localhost:5432"
echo ""

# 6. 显示有用的命令
echo "=========================================="
echo "           有用的命令"
echo "=========================================="
echo "查看服务状态:    cd $SUPABASE_DIR && docker-compose ps"
echo "查看服务日志:    cd $SUPABASE_DIR && docker-compose logs"
echo "重启服务:        cd $SUPABASE_DIR && docker-compose restart"
echo "停止服务:        cd $SUPABASE_DIR && docker-compose down"
echo "启动服务:        cd $SUPABASE_DIR && docker-compose up -d"
echo ""
echo "完整测试:        ./test_supabase.sh"
echo "=========================================="

# 7. 检查凭据文件
if [[ -f "$SUPABASE_DIR/supabase_credentials.txt" ]]; then
    echo ""
    log_info "凭据文件位置: $SUPABASE_DIR/supabase_credentials.txt"
    echo "使用以下命令查看凭据:"
    echo "cat $SUPABASE_DIR/supabase_credentials.txt"
fi

echo ""
log_info "快速检查完成！如需详细测试，请运行: ./test_supabase.sh"

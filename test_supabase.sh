#!/bin/bash

# Supabase 服务测试脚本
# 用于验证通过 deploy_supabase.sh 部署的 Supabase 实例功能
# 作者: Claude
# 版本: 1.0

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # 无颜色

# 测试结果统计
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# 配置变量
SUPABASE_DIR="$HOME/supabase/docker"
STUDIO_URL="http://localhost:3000"
API_URL="http://localhost:8000"
POSTGRES_HOST="localhost"
POSTGRES_PORT="5432"

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[PASS]${NC} $1"
}

log_fail() {
    echo -e "${RED}[FAIL]${NC} $1"
}

log_test() {
    echo -e "${BLUE}[TEST]${NC} $1"
}

log_step() {
    echo -e "${CYAN}[STEP]${NC} $1"
}

# 测试结果记录
record_test() {
    local test_name="$1"
    local result="$2"
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    
    if [[ "$result" == "PASS" ]]; then
        PASSED_TESTS=$((PASSED_TESTS + 1))
        log_success "$test_name"
    else
        FAILED_TESTS=$((FAILED_TESTS + 1))
        log_fail "$test_name"
    fi
}

# 检查命令是否存在
check_command() {
    if command -v "$1" &> /dev/null; then
        return 0
    else
        return 1
    fi
}

# 检查端口是否开放
check_port() {
    local host="$1"
    local port="$2"
    local timeout="${3:-5}"
    
    if timeout "$timeout" bash -c "echo >/dev/tcp/$host/$port" 2>/dev/null; then
        return 0
    else
        return 1
    fi
}

# 检查HTTP端点
check_http_endpoint() {
    local url="$1"
    local timeout="${2:-10}"
    local expected_status="${3:-200}"

    if check_command curl; then
        local response=$(curl -s -o /dev/null -w "%{http_code}" --connect-timeout "$timeout" "$url" 2>/dev/null)
        if [[ "$response" == "$expected_status" ]]; then
            return 0
        else
            echo "HTTP状态码: $response (期望: $expected_status)"
            return 1
        fi
    else
        log_error "curl命令未找到，无法测试HTTP端点"
        return 1
    fi
}

# 检查HTTP端点 (允许重定向)
check_http_endpoint_with_redirect() {
    local url="$1"
    local timeout="${2:-10}"

    if check_command curl; then
        local response=$(curl -s -o /dev/null -w "%{http_code}" --connect-timeout "$timeout" "$url" 2>/dev/null)
        # 接受 2xx 和 3xx 状态码作为正常响应
        if [[ "$response" =~ ^[23][0-9][0-9]$ ]]; then
            return 0
        else
            echo "HTTP状态码: $response"
            return 1
        fi
    else
        log_error "curl命令未找到，无法测试HTTP端点"
        return 1
    fi
}

# 测试Docker服务状态
test_docker_services() {
    log_step "测试Docker服务状态..."
    
    # 检查Docker是否运行
    log_test "检查Docker服务状态"
    if systemctl is-active --quiet docker; then
        record_test "Docker服务运行状态" "PASS"
    else
        record_test "Docker服务运行状态" "FAIL"
        log_error "Docker服务未运行"
        return 1
    fi
    
    # 检查Supabase目录
    log_test "检查Supabase安装目录"
    if [[ -d "$SUPABASE_DIR" ]]; then
        record_test "Supabase目录存在" "PASS"
    else
        record_test "Supabase目录存在" "FAIL"
        log_error "Supabase目录不存在: $SUPABASE_DIR"
        return 1
    fi
    
    # 检查docker-compose.yml文件
    log_test "检查docker-compose配置文件"
    if [[ -f "$SUPABASE_DIR/docker-compose.yml" ]]; then
        record_test "docker-compose.yml存在" "PASS"
    else
        record_test "docker-compose.yml存在" "FAIL"
        log_error "docker-compose.yml文件不存在"
        return 1
    fi
    
    # 检查.env文件
    log_test "检查环境配置文件"
    if [[ -f "$SUPABASE_DIR/.env" ]]; then
        record_test ".env文件存在" "PASS"
    else
        record_test ".env文件存在" "FAIL"
        log_error ".env文件不存在"
        return 1
    fi
    
    # 检查Docker Compose服务状态
    log_test "检查Docker Compose服务状态"
    cd "$SUPABASE_DIR"
    
    if docker-compose ps | grep -q "Up"; then
        record_test "Docker Compose服务运行" "PASS"
        echo "运行中的服务:"
        docker-compose ps --format "table {{.Name}}\t{{.State}}\t{{.Ports}}"
    else
        record_test "Docker Compose服务运行" "FAIL"
        log_error "没有检测到运行中的Docker Compose服务"
        echo "当前服务状态:"
        docker-compose ps
        return 1
    fi
}

# 测试端口连通性
test_port_connectivity() {
    log_step "测试端口连通性..."
    
    # 测试Supabase Studio端口 (3000)
    log_test "测试Supabase Studio端口 (3000)"
    if check_port "localhost" "3000"; then
        record_test "端口3000连通性" "PASS"
    else
        record_test "端口3000连通性" "FAIL"
        log_error "无法连接到端口3000 (Supabase Studio)"
        
        # 检查端口是否被其他进程占用
        if check_command netstat; then
            echo "端口3000使用情况:"
            netstat -tlnp | grep :3000 || echo "端口3000未被占用"
        fi
        
        # 检查Docker容器端口映射
        echo "Docker容器端口映射:"
        docker ps --format "table {{.Names}}\t{{.Ports}}" | grep 3000 || echo "没有容器映射到端口3000"
    fi
    
    # 测试API Gateway端口 (8000)
    log_test "测试API Gateway端口 (8000)"
    if check_port "localhost" "8000"; then
        record_test "端口8000连通性" "PASS"
    else
        record_test "端口8000连通性" "FAIL"
        log_error "无法连接到端口8000 (API Gateway)"
    fi
    
    # 测试PostgreSQL端口 (5432)
    log_test "测试PostgreSQL端口 (5432)"
    if check_port "localhost" "5432"; then
        record_test "端口5432连通性" "PASS"
    else
        record_test "端口5432连通性" "FAIL"
        log_error "无法连接到端口5432 (PostgreSQL)"
    fi
}

# 测试HTTP端点
test_http_endpoints() {
    log_step "测试HTTP端点..."

    # 测试Supabase Studio
    log_test "测试Supabase Studio HTTP响应"
    if check_http_endpoint_with_redirect "$STUDIO_URL"; then
        record_test "Supabase Studio HTTP响应" "PASS"
    else
        record_test "Supabase Studio HTTP响应" "FAIL"
        log_error "Supabase Studio无法访问: $STUDIO_URL"
    fi

    # 测试API Gateway健康检查
    log_test "测试API Gateway健康检查"
    if check_http_endpoint "$API_URL/health"; then
        record_test "API Gateway健康检查" "PASS"
    else
        # 尝试根路径 (Kong网关通常返回404，这是正常的)
        local root_response=$(curl -s -o /dev/null -w "%{http_code}" --connect-timeout 10 "$API_URL" 2>/dev/null)
        if [[ "$root_response" =~ ^[2-4][0-9][0-9]$ ]]; then
            record_test "API Gateway根路径响应" "PASS"
            echo "API Gateway响应正常 (状态码: $root_response)"
        else
            record_test "API Gateway响应" "FAIL"
            log_error "API Gateway无法访问: $API_URL (状态码: $root_response)"
        fi
    fi
}

# 测试数据库连接
test_database_connection() {
    log_step "测试数据库连接..."

    # 读取数据库凭据
    local db_password=""
    if [[ -f "$SUPABASE_DIR/.env" ]]; then
        db_password=$(grep "POSTGRES_PASSWORD=" "$SUPABASE_DIR/.env" | cut -d'=' -f2)
    fi

    if [[ -z "$db_password" ]]; then
        log_warn "无法从.env文件读取数据库密码"
        record_test "数据库密码读取" "FAIL"
        return 1
    fi

    # 测试PostgreSQL连接
    log_test "测试PostgreSQL数据库连接"
    if check_command psql; then
        if PGPASSWORD="$db_password" psql -h "$POSTGRES_HOST" -p "$POSTGRES_PORT" -U postgres -d postgres -c "SELECT version();" &>/dev/null; then
            record_test "PostgreSQL连接" "PASS"

            # 获取数据库版本信息
            local db_version=$(PGPASSWORD="$db_password" psql -h "$POSTGRES_HOST" -p "$POSTGRES_PORT" -U postgres -d postgres -t -c "SELECT version();" 2>/dev/null | head -1 | xargs)
            echo "数据库版本: $db_version"
        else
            record_test "PostgreSQL连接" "FAIL"
            log_error "无法连接到PostgreSQL数据库"
        fi
    else
        log_warn "psql命令未找到，跳过数据库连接测试"
        record_test "PostgreSQL客户端可用性" "FAIL"
    fi
}

# 测试API端点
test_api_endpoints() {
    log_step "测试API端点..."

    # 读取API密钥
    local anon_key=""
    if [[ -f "$SUPABASE_DIR/.env" ]]; then
        anon_key=$(grep "ANON_KEY=" "$SUPABASE_DIR/.env" | cut -d'=' -f2)
    fi

    if [[ -z "$anon_key" ]]; then
        log_warn "无法从.env文件读取API密钥"
        record_test "API密钥读取" "FAIL"
        return 1
    fi

    # 测试REST API
    log_test "测试REST API端点"
    local rest_url="$API_URL/rest/v1/"
    if curl -s -H "apikey: $anon_key" -H "Authorization: Bearer $anon_key" "$rest_url" &>/dev/null; then
        record_test "REST API响应" "PASS"
    else
        record_test "REST API响应" "FAIL"
        log_error "REST API无法访问: $rest_url"
    fi

    # 测试Auth API
    log_test "测试Auth API端点"
    local auth_url="$API_URL/auth/v1/settings"
    if curl -s -H "apikey: $anon_key" "$auth_url" &>/dev/null; then
        record_test "Auth API响应" "PASS"
    else
        record_test "Auth API响应" "FAIL"
        log_error "Auth API无法访问: $auth_url"
    fi

    # 测试Storage API
    log_test "测试Storage API端点"
    local storage_url="$API_URL/storage/v1/bucket"
    if curl -s -H "apikey: $anon_key" -H "Authorization: Bearer $anon_key" "$storage_url" &>/dev/null; then
        record_test "Storage API响应" "PASS"
    else
        record_test "Storage API响应" "FAIL"
        log_error "Storage API无法访问: $storage_url"
    fi
}

# 测试实时功能
test_realtime() {
    log_step "测试实时功能..."

    # 检查WebSocket端点
    log_test "测试Realtime WebSocket端点"
    local realtime_url="ws://localhost:4000/socket/websocket"

    if check_command wscat; then
        if timeout 5 wscat -c "$realtime_url" -x '{"topic":"phoenix","event":"heartbeat","payload":{},"ref":"1"}' &>/dev/null; then
            record_test "Realtime WebSocket连接" "PASS"
        else
            record_test "Realtime WebSocket连接" "FAIL"
            log_error "无法连接到Realtime WebSocket"
        fi
    else
        log_warn "wscat命令未找到，跳过WebSocket测试"
        record_test "WebSocket客户端可用性" "FAIL"
    fi

    # 检查Realtime HTTP端点
    log_test "测试Realtime HTTP端点"
    if check_port "localhost" "4000"; then
        record_test "Realtime端口连通性" "PASS"
    else
        record_test "Realtime端口连通性" "FAIL"
        log_error "无法连接到Realtime端口4000"
    fi
}

# 诊断端口映射问题
diagnose_port_mapping() {
    log_step "诊断端口映射问题..."

    echo "=== Docker容器状态 ==="
    docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"

    echo ""
    echo "=== 端口监听状态 ==="
    if check_command netstat; then
        echo "监听中的端口:"
        netstat -tlnp | grep -E ":(3000|8000|5432|4000)" || echo "未找到相关端口监听"
    elif check_command ss; then
        echo "监听中的端口:"
        ss -tlnp | grep -E ":(3000|8000|5432|4000)" || echo "未找到相关端口监听"
    else
        log_warn "netstat和ss命令都未找到，无法检查端口监听状态"
    fi

    echo ""
    echo "=== Docker网络信息 ==="
    docker network ls

    echo ""
    echo "=== 防火墙状态 ==="
    if check_command ufw; then
        echo "UFW状态:"
        sudo ufw status
    elif check_command iptables; then
        echo "iptables规则 (仅显示INPUT链):"
        sudo iptables -L INPUT -n | head -10
    else
        log_warn "无法检查防火墙状态"
    fi
}

# 生成测试报告
generate_test_report() {
    echo ""
    echo "=========================================="
    echo "           测试结果报告"
    echo "=========================================="
    echo "总测试数: $TOTAL_TESTS"
    echo "通过测试: $PASSED_TESTS"
    echo "失败测试: $FAILED_TESTS"
    echo ""

    if [[ $FAILED_TESTS -eq 0 ]]; then
        log_success "所有测试通过！Supabase实例运行正常。"
    else
        log_warn "有 $FAILED_TESTS 个测试失败，请检查上述错误信息。"

        echo ""
        echo "常见问题解决方案:"
        echo "1. 端口3000无法访问:"
        echo "   - 检查Docker容器是否正确映射端口"
        echo "   - 运行: cd ~/supabase/docker && docker-compose restart"
        echo "   - 检查防火墙设置: sudo ufw allow 3000"
        echo ""
        echo "2. 数据库连接失败:"
        echo "   - 确认PostgreSQL容器正在运行"
        echo "   - 检查.env文件中的数据库密码"
        echo "   - 安装PostgreSQL客户端: sudo apt install postgresql-client"
        echo ""
        echo "3. API端点无响应:"
        echo "   - 检查Kong网关容器状态"
        echo "   - 验证API密钥配置"
        echo "   - 查看容器日志: docker-compose logs"
    fi

    echo "=========================================="
}

# 显示交互式菜单
show_menu() {
    echo ""
    echo "=========================================="
    echo "      Supabase 服务测试工具"
    echo "=========================================="
    echo "请选择要执行的测试:"
    echo ""
    echo "1) 完整测试 (推荐)"
    echo "2) Docker服务状态测试"
    echo "3) 端口连通性测试"
    echo "4) HTTP端点测试"
    echo "5) 数据库连接测试"
    echo "6) API端点测试"
    echo "7) 实时功能测试"
    echo "8) 端口映射诊断"
    echo "9) 生成详细报告"
    echo "0) 退出"
    echo ""
}

# 执行选定的测试
run_selected_test() {
    local choice="$1"

    case $choice in
        1)
            log_info "执行完整测试..."
            test_docker_services
            test_port_connectivity
            test_http_endpoints
            test_database_connection
            test_api_endpoints
            test_realtime
            ;;
        2)
            test_docker_services
            ;;
        3)
            test_port_connectivity
            ;;
        4)
            test_http_endpoints
            ;;
        5)
            test_database_connection
            ;;
        6)
            test_api_endpoints
            ;;
        7)
            test_realtime
            ;;
        8)
            diagnose_port_mapping
            return 0  # 诊断不计入测试统计
            ;;
        9)
            log_info "生成详细系统报告..."
            test_docker_services
            test_port_connectivity
            diagnose_port_mapping
            ;;
        0)
            log_info "退出测试工具"
            exit 0
            ;;
        *)
            log_error "无效选择: $choice"
            return 1
            ;;
    esac
}

# 主函数
main() {
    echo "Supabase服务测试工具启动..."

    # 检查基本依赖
    if ! check_command docker; then
        log_error "Docker未安装或不在PATH中"
        exit 1
    fi

    if ! check_command docker-compose; then
        log_error "Docker Compose未安装或不在PATH中"
        exit 1
    fi

    # 交互式模式
    if [[ $# -eq 0 ]]; then
        while true; do
            show_menu
            read -p "请选择 [0-9]: " choice
            echo ""

            # 重置测试统计（除了诊断模式）
            if [[ "$choice" != "8" ]]; then
                TOTAL_TESTS=0
                PASSED_TESTS=0
                FAILED_TESTS=0
            fi

            run_selected_test "$choice"

            if [[ "$choice" != "0" && "$choice" != "8" ]]; then
                generate_test_report
                echo ""
                read -p "按Enter键继续..."
            fi
        done
    else
        # 命令行模式
        case "$1" in
            --full|--all)
                log_info "执行完整测试..."
                run_selected_test "1"
                generate_test_report
                ;;
            --docker)
                run_selected_test "2"
                generate_test_report
                ;;
            --ports)
                run_selected_test "3"
                generate_test_report
                ;;
            --http)
                run_selected_test "4"
                generate_test_report
                ;;
            --database|--db)
                run_selected_test "5"
                generate_test_report
                ;;
            --api)
                run_selected_test "6"
                generate_test_report
                ;;
            --realtime)
                run_selected_test "7"
                generate_test_report
                ;;
            --diagnose)
                run_selected_test "8"
                ;;
            --help|-h)
                echo "用法: $0 [选项]"
                echo ""
                echo "选项:"
                echo "  --full, --all     执行完整测试"
                echo "  --docker          测试Docker服务状态"
                echo "  --ports           测试端口连通性"
                echo "  --http            测试HTTP端点"
                echo "  --database, --db  测试数据库连接"
                echo "  --api             测试API端点"
                echo "  --realtime        测试实时功能"
                echo "  --diagnose        诊断端口映射问题"
                echo "  --help, -h        显示此帮助信息"
                echo ""
                echo "不带参数运行将进入交互式模式"
                ;;
            *)
                log_error "未知选项: $1"
                echo "使用 --help 查看可用选项"
                exit 1
                ;;
        esac
    fi
}

# 执行主函数
main "$@"

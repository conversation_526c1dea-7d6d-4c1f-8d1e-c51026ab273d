#!/bin/bash

# Docker 权限修复脚本
# 自动检测和修复 Docker 权限问题
# 作者: Claude

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() { echo -e "${GREEN}[INFO]${NC} $1"; }
log_warn() { echo -e "${YELLOW}[WARN]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }
log_step() { echo -e "${BLUE}[STEP]${NC} $1"; }

# 检查是否为root用户
check_root() {
    if [[ $EUID -eq 0 ]]; then
        log_error "请不要使用root用户运行此脚本"
        log_info "使用普通用户运行: ./fix_docker_permissions.sh"
        exit 1
    fi
}

# 检查Docker服务状态
check_docker_service() {
    log_step "检查Docker服务状态..."
    
    if systemctl is-active --quiet docker; then
        log_info "Docker服务正在运行 ✓"
        return 0
    else
        log_warn "Docker服务未运行"
        
        # 尝试启动Docker服务
        log_info "尝试启动Docker服务..."
        if sudo systemctl start docker; then
            log_info "Docker服务启动成功 ✓"
            
            # 设置开机自启
            sudo systemctl enable docker
            log_info "已设置Docker开机自启 ✓"
            return 0
        else
            log_error "无法启动Docker服务"
            return 1
        fi
    fi
}

# 检查Docker权限
check_docker_permissions() {
    log_step "检查Docker权限..."
    
    # 测试普通用户权限
    if docker ps &>/dev/null; then
        log_info "普通用户可以运行Docker命令 ✓"
        return 0
    fi
    
    # 测试sudo权限
    if sudo docker ps &>/dev/null; then
        log_warn "需要sudo权限才能运行Docker命令"
        return 1
    else
        log_error "即使使用sudo也无法运行Docker命令"
        return 2
    fi
}

# 检查用户是否在docker组中
check_docker_group() {
    log_step "检查用户组配置..."
    
    if groups $USER | grep -q docker; then
        log_info "用户 $USER 已在docker组中 ✓"
        return 0
    else
        log_warn "用户 $USER 不在docker组中"
        return 1
    fi
}

# 添加用户到docker组
add_user_to_docker_group() {
    log_step "添加用户到docker组..."
    
    if sudo usermod -aG docker $USER; then
        log_info "用户 $USER 已添加到docker组 ✓"
        return 0
    else
        log_error "无法添加用户到docker组"
        return 1
    fi
}

# 验证权限修复
verify_permissions() {
    log_step "验证权限修复..."
    
    # 使用newgrp临时切换组
    log_info "测试新的组权限..."
    
    if newgrp docker << 'EOF'
docker ps &>/dev/null
exit $?
EOF
    then
        log_info "权限修复成功！可以使用普通用户权限运行Docker ✓"
        return 0
    else
        log_warn "权限修复后仍需要sudo权限"
        return 1
    fi
}

# 提供解决方案
provide_solutions() {
    log_step "提供解决方案..."
    
    echo ""
    echo "=========================================="
    echo "           Docker权限解决方案"
    echo "=========================================="
    echo ""
    
    echo "方案1: 重新登录 (推荐)"
    echo "  1. 退出当前终端会话"
    echo "  2. 重新登录系统"
    echo "  3. 重新运行Supabase脚本"
    echo ""
    
    echo "方案2: 使用newgrp命令"
    echo "  运行: newgrp docker"
    echo "  然后在新的shell中运行Supabase脚本"
    echo ""
    
    echo "方案3: 使用sudo权限"
    echo "  所有Docker命令前加sudo"
    echo "  例如: sudo docker-compose up -d"
    echo ""
    
    echo "方案4: 重启系统 (最彻底)"
    echo "  重启后组权限会完全生效"
    echo ""
    
    echo "=========================================="
    echo "           自动修复的脚本"
    echo "=========================================="
    echo ""
    echo "我们的脚本已经自动适配了这些情况:"
    echo "✓ deploy_supabase.sh - 自动检测并使用适当权限"
    echo "✓ supabase_manager.sh - 支持sudo和普通用户权限"
    echo "✓ deploy_auth_db.sh - 通过模块管理器使用正确权限"
    echo ""
    echo "您可以直接运行这些脚本，它们会自动处理权限问题。"
}

# 创建权限测试脚本
create_test_script() {
    log_step "创建权限测试脚本..."
    
    cat > test_docker_permissions.sh << 'EOF'
#!/bin/bash

# Docker权限测试脚本

echo "=== Docker权限测试 ==="

echo "1. 测试普通用户权限:"
if docker ps &>/dev/null; then
    echo "   ✓ 普通用户可以运行Docker命令"
    DOCKER_CMD="docker"
    COMPOSE_CMD="docker-compose"
else
    echo "   ✗ 普通用户无法运行Docker命令"
    
    echo "2. 测试sudo权限:"
    if sudo docker ps &>/dev/null; then
        echo "   ✓ sudo权限可以运行Docker命令"
        DOCKER_CMD="sudo docker"
        COMPOSE_CMD="sudo docker-compose"
    else
        echo "   ✗ 即使sudo也无法运行Docker命令"
        echo "   请检查Docker安装和服务状态"
        exit 1
    fi
fi

echo ""
echo "推荐的命令前缀:"
echo "  Docker命令: $DOCKER_CMD"
echo "  Compose命令: $COMPOSE_CMD"

echo ""
echo "测试Docker Compose:"
if command -v docker-compose &>/dev/null; then
    echo "   ✓ docker-compose 已安装"
    if $COMPOSE_CMD --version &>/dev/null; then
        echo "   ✓ docker-compose 可以运行"
    else
        echo "   ✗ docker-compose 无法运行"
    fi
else
    echo "   ✗ docker-compose 未安装"
fi

echo ""
echo "=== 测试完成 ==="
EOF
    
    chmod +x test_docker_permissions.sh
    log_info "权限测试脚本已创建: test_docker_permissions.sh"
}

# 主函数
main() {
    echo "=========================================="
    echo "        Docker 权限修复工具"
    echo "=========================================="
    echo ""
    
    # 检查是否为root用户
    check_root
    
    # 检查Docker服务
    if ! check_docker_service; then
        log_error "Docker服务问题，请先解决Docker安装问题"
        exit 1
    fi
    
    # 检查Docker权限
    check_docker_permissions
    local permission_status=$?
    
    case $permission_status in
        0)
            log_info "Docker权限正常，无需修复 ✓"
            ;;
        1)
            log_warn "需要修复Docker权限"
            
            # 检查用户组
            if ! check_docker_group; then
                # 添加用户到docker组
                if add_user_to_docker_group; then
                    log_info "用户已添加到docker组"
                    
                    # 验证权限
                    if verify_permissions; then
                        log_info "权限修复成功！"
                    else
                        log_warn "权限修复需要重新登录才能完全生效"
                    fi
                else
                    log_error "无法添加用户到docker组"
                fi
            else
                log_warn "用户已在docker组中，但权限未生效"
                log_info "这通常需要重新登录才能解决"
            fi
            ;;
        2)
            log_error "Docker权限严重问题，请检查Docker安装"
            exit 1
            ;;
    esac
    
    # 创建测试脚本
    create_test_script
    
    # 提供解决方案
    provide_solutions
    
    echo ""
    echo "=========================================="
    echo "           修复完成"
    echo "=========================================="
    echo ""
    
    log_info "Docker权限修复脚本执行完成！"
    log_info "运行 ./test_docker_permissions.sh 来测试权限状态"
    
    # 最终权限检查
    echo ""
    log_step "最终权限检查:"
    if docker ps &>/dev/null; then
        log_info "✓ 可以直接运行 Docker 命令"
        log_info "✓ 可以直接运行 Supabase 脚本"
    else
        log_warn "仍需要 sudo 权限或重新登录"
        log_info "Supabase 脚本会自动使用 sudo 权限"
    fi
}

# 执行主函数
main "$@"

#!/bin/bash

# Supabase 配置一致性检查脚本
# 验证 deploy_supabase.sh 和 supabase_manager.sh 的配置一致性
# 作者: Claude

# 不使用 set -e，因为我们需要继续执行所有检查

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() { echo -e "${GREEN}[INFO]${NC} $1"; }
log_warn() { echo -e "${YELLOW}[WARN]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }
log_check() { echo -e "${BLUE}[CHECK]${NC} $1"; }

# 配置文件路径
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
DEPLOY_SCRIPT="$SCRIPT_DIR/deploy_supabase.sh"
MANAGER_SCRIPT="$SCRIPT_DIR/supabase_manager.sh"
QUICK_DEPLOY_SCRIPT="$SCRIPT_DIR/deploy_auth_db.sh"

# 检查脚本文件存在性
check_script_files() {
    log_check "检查脚本文件存在性..."
    
    local missing_files=()
    
    if [[ ! -f "$DEPLOY_SCRIPT" ]]; then
        missing_files+=("deploy_supabase.sh")
    fi
    
    if [[ ! -f "$MANAGER_SCRIPT" ]]; then
        missing_files+=("supabase_manager.sh")
    fi
    
    if [[ ! -f "$QUICK_DEPLOY_SCRIPT" ]]; then
        missing_files+=("deploy_auth_db.sh")
    fi
    
    if [[ ${#missing_files[@]} -gt 0 ]]; then
        log_error "缺少脚本文件: ${missing_files[*]}"
        return 1
    else
        log_info "所有脚本文件存在 ✓"
        return 0
    fi
}

# 检查Supabase目录配置
check_supabase_directory() {
    log_check "检查Supabase目录配置..."
    
    # 从脚本中提取目录配置
    local deploy_dir=$(grep "SUPABASE_DIR=" "$DEPLOY_SCRIPT" | head -1 | cut -d'=' -f2 | tr -d '"')
    local manager_dir=$(grep "SUPABASE_DIR=" "$MANAGER_SCRIPT" | head -1 | cut -d'=' -f2 | tr -d '"')
    local quick_dir=$(grep "SUPABASE_DIR=" "$QUICK_DEPLOY_SCRIPT" | head -1 | cut -d'=' -f2 | tr -d '"')
    
    echo "目录配置对比:"
    echo "  deploy_supabase.sh:  $deploy_dir"
    echo "  supabase_manager.sh: $manager_dir"
    echo "  deploy_auth_db.sh:   $quick_dir"
    
    if [[ "$deploy_dir" == "$manager_dir" && "$manager_dir" == "$quick_dir" ]]; then
        log_info "Supabase目录配置一致 ✓"
        return 0
    else
        log_error "Supabase目录配置不一致"
        return 1
    fi
}

# 检查Docker权限处理
check_docker_permissions() {
    log_check "检查Docker权限处理..."
    
    # 检查deploy_supabase.sh中的权限处理
    if grep -q "check_docker_permissions" "$DEPLOY_SCRIPT"; then
        log_info "deploy_supabase.sh 包含Docker权限检查 ✓"
    else
        log_warn "deploy_supabase.sh 缺少Docker权限检查"
    fi
    
    # 检查supabase_manager.sh中的权限处理
    if grep -q "check_docker_permissions" "$MANAGER_SCRIPT"; then
        log_info "supabase_manager.sh 包含Docker权限检查 ✓"
    else
        log_warn "supabase_manager.sh 缺少Docker权限检查"
    fi
    
    # 检查sudo命令使用
    local deploy_sudo_count=$(grep -c "sudo docker" "$DEPLOY_SCRIPT" || true)
    local manager_sudo_count=$(grep -c "sudo docker" "$MANAGER_SCRIPT" || true)
    
    echo "sudo docker命令使用次数:"
    echo "  deploy_supabase.sh:  $deploy_sudo_count"
    echo "  supabase_manager.sh: $manager_sudo_count"
    
    if [[ $deploy_sudo_count -gt 0 && $manager_sudo_count -gt 0 ]]; then
        log_info "两个脚本都支持sudo权限 ✓"
    else
        log_warn "权限处理可能不一致"
    fi
}

# 检查端口配置
check_port_configuration() {
    log_check "检查端口配置..."
    
    # 常见端口列表
    local ports=("3000" "8000" "5432" "4000" "8080" "9999")
    
    echo "端口使用情况:"
    for port in "${ports[@]}"; do
        local deploy_count=$(grep -c ":$port" "$DEPLOY_SCRIPT" || true)
        local manager_count=$(grep -c ":$port" "$MANAGER_SCRIPT" || true)
        local quick_count=$(grep -c ":$port" "$QUICK_DEPLOY_SCRIPT" || true)
        
        echo "  端口 $port:"
        echo "    deploy_supabase.sh:  $deploy_count 次"
        echo "    supabase_manager.sh: $manager_count 次"
        echo "    deploy_auth_db.sh:   $quick_count 次"
    done
}

# 检查环境变量配置
check_environment_variables() {
    log_check "检查环境变量配置..."
    
    # 常见环境变量
    local env_vars=("POSTGRES_PASSWORD" "JWT_SECRET" "ANON_KEY" "SERVICE_ROLE_KEY" "API_EXTERNAL_URL")
    
    echo "环境变量使用情况:"
    for var in "${env_vars[@]}"; do
        local deploy_count=$(grep -c "$var" "$DEPLOY_SCRIPT" || true)
        local manager_count=$(grep -c "$var" "$MANAGER_SCRIPT" || true)
        local quick_count=$(grep -c "$var" "$QUICK_DEPLOY_SCRIPT" || true)
        
        echo "  $var:"
        echo "    deploy_supabase.sh:  $deploy_count 次"
        echo "    supabase_manager.sh: $manager_count 次"
        echo "    deploy_auth_db.sh:   $quick_count 次"
    done
}

# 检查服务名称映射
check_service_names() {
    log_check "检查服务名称映射..."
    
    # 从supabase_manager.sh提取服务映射
    if grep -A 20 "映射模块名到实际服务名" "$MANAGER_SCRIPT" &>/dev/null; then
        log_info "supabase_manager.sh 包含服务名称映射 ✓"
    else
        log_warn "supabase_manager.sh 缺少服务名称映射"
    fi
    
    # 检查常见服务名
    local services=("db" "auth" "rest" "kong" "meta" "analytics" "vector")
    
    echo "服务名称使用情况:"
    for service in "${services[@]}"; do
        local deploy_count=$(grep -c "supabase-$service" "$DEPLOY_SCRIPT" || true)
        local manager_count=$(grep -c "\"$service\"" "$MANAGER_SCRIPT" || true)
        
        echo "  $service: deploy($deploy_count) manager($manager_count)"
    done
}

# 检查凭据文件配置
check_credentials_configuration() {
    log_check "检查凭据文件配置..."
    
    # 检查凭据文件路径
    local deploy_cred=$(grep -o "supabase_credentials\.txt" "$DEPLOY_SCRIPT" | head -1 || echo "未找到")
    local quick_cred=$(grep -o "auth_db_credentials\.txt" "$QUICK_DEPLOY_SCRIPT" | head -1 || echo "未找到")
    
    echo "凭据文件配置:"
    echo "  deploy_supabase.sh:  $deploy_cred"
    echo "  deploy_auth_db.sh:   $quick_cred"
    
    if [[ "$deploy_cred" != "未找到" && "$quick_cred" != "未找到" ]]; then
        log_info "凭据文件配置存在 ✓"
    else
        log_warn "某些脚本缺少凭据文件配置"
    fi
}

# 检查错误处理
check_error_handling() {
    log_check "检查错误处理..."
    
    # 检查set -e使用
    local deploy_set_e=$(grep -c "set -e" "$DEPLOY_SCRIPT" || true)
    local manager_set_e=$(grep -c "set -e" "$MANAGER_SCRIPT" || true)
    local quick_set_e=$(grep -c "set -e" "$QUICK_DEPLOY_SCRIPT" || true)
    
    echo "错误处理配置 (set -e):"
    echo "  deploy_supabase.sh:  $deploy_set_e"
    echo "  supabase_manager.sh: $manager_set_e"
    echo "  deploy_auth_db.sh:   $quick_set_e"
    
    # 检查日志函数
    local deploy_log=$(grep -c "log_error\|log_warn\|log_info" "$DEPLOY_SCRIPT" || true)
    local manager_log=$(grep -c "log_error\|log_warn\|log_info" "$MANAGER_SCRIPT" || true)
    local quick_log=$(grep -c "log_error\|log_warn\|log_info" "$QUICK_DEPLOY_SCRIPT" || true)
    
    echo "日志函数使用次数:"
    echo "  deploy_supabase.sh:  $deploy_log"
    echo "  supabase_manager.sh: $manager_log"
    echo "  deploy_auth_db.sh:   $quick_log"
}

# 生成修复建议
generate_fix_suggestions() {
    log_check "生成修复建议..."
    
    echo ""
    echo "=========================================="
    echo "           修复建议"
    echo "=========================================="
    echo ""
    
    echo "1. Docker权限问题修复:"
    echo "   - 确保用户在docker组中: sudo usermod -aG docker \$USER"
    echo "   - 重新登录或运行: newgrp docker"
    echo "   - 或者使用sudo运行Docker命令"
    echo ""
    
    echo "2. 配置一致性建议:"
    echo "   - 统一使用相同的Supabase目录路径"
    echo "   - 确保所有脚本使用相同的端口配置"
    echo "   - 统一环境变量命名和使用"
    echo ""
    
    echo "3. 权限处理改进:"
    echo "   - 在所有脚本中添加Docker权限检查"
    echo "   - 提供sudo和普通用户两种运行模式"
    echo "   - 添加权限问题的自动修复功能"
    echo ""
    
    echo "4. 错误处理增强:"
    echo "   - 添加更详细的错误信息"
    echo "   - 提供问题解决建议"
    echo "   - 添加回滚机制"
}

# 主函数
main() {
    echo "=========================================="
    echo "    Supabase 配置一致性检查工具"
    echo "=========================================="
    echo ""
    
    local checks_passed=0
    local total_checks=8
    
    # 执行所有检查
    if check_script_files; then ((checks_passed++)); fi
    echo ""
    
    if check_supabase_directory; then ((checks_passed++)); fi
    echo ""
    
    if check_docker_permissions; then ((checks_passed++)); fi
    echo ""
    
    check_port_configuration
    ((checks_passed++))  # 信息性检查，总是通过
    echo ""
    
    check_environment_variables
    ((checks_passed++))  # 信息性检查，总是通过
    echo ""
    
    check_service_names
    ((checks_passed++))  # 信息性检查，总是通过
    echo ""
    
    if check_credentials_configuration; then ((checks_passed++)); fi
    echo ""
    
    check_error_handling
    ((checks_passed++))  # 信息性检查，总是通过
    echo ""
    
    # 显示结果
    echo "=========================================="
    echo "           检查结果总结"
    echo "=========================================="
    echo "通过检查: $checks_passed/$total_checks"
    
    if [[ $checks_passed -eq $total_checks ]]; then
        log_info "所有检查通过！配置一致性良好。"
    else
        log_warn "部分检查未通过，建议查看上述详细信息。"
    fi
    
    generate_fix_suggestions
    
    echo ""
    log_info "配置一致性检查完成！"
}

# 执行主函数
main "$@"

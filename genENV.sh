#!/bin/bash

# Supabase Docker 环境配置生成脚本
# 使用方法: ./genEnv.sh .env.example .env

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${PURPLE}========================================${NC}"
    echo -e "${PURPLE}  Supabase Docker 环境配置生成器${NC}"
    echo -e "${PURPLE}========================================${NC}"
}

# 检查参数
if [ $# -ne 2 ]; then
    print_error "使用方法: $0 <source_file> <target_file>"
    print_info "示例: $0 .env.example .env"
    exit 1
fi

SOURCE_FILE="$1"
TARGET_FILE="$2"

print_header

# 检查源文件是否存在
if [ ! -f "$SOURCE_FILE" ]; then
    print_error "源文件 '$SOURCE_FILE' 不存在！"
    exit 1
fi

print_info "正在从 '$SOURCE_FILE' 生成 '$TARGET_FILE'..."

# 检查必要的工具
check_dependencies() {
    local required_deps=("openssl")
    local optional_deps=("node" "python3")
    local missing_required=()
    local missing_optional=()
    
    # 检查必需的依赖
    for dep in "${required_deps[@]}"; do
        if ! command -v "$dep" &> /dev/null; then
            missing_required+=("$dep")
        fi
    done
    
    # 检查可选的依赖
    for dep in "${optional_deps[@]}"; do
        if ! command -v "$dep" &> /dev/null; then
            missing_optional+=("$dep")
        fi
    done
    
    if [ ${#missing_required[@]} -gt 0 ]; then
        print_error "缺少必要的依赖工具: ${missing_required[*]}"
        print_info "请安装缺少的工具后重新运行脚本"
        exit 1
    fi
    
    if [ ${#missing_optional[@]} -eq ${#optional_deps[@]} ]; then
        print_warning "缺少可选工具 (${missing_optional[*]})，API 密钥需要手动生成"
    fi
}

print_info "检查依赖工具..."
check_dependencies

# 生成各种密钥
print_info "正在生成安全密钥..."

# 生成 JWT 密钥 (64字符)
JWT_SECRET=$(openssl rand -base64 64 | tr -d "=+/" | cut -c1-64)
print_success "✓ JWT 密钥已生成"

# 生成数据库密码 (32字符)
POSTGRES_PASSWORD=$(openssl rand -base64 32 | tr -d "=+/" | cut -c1-32)
print_success "✓ 数据库密码已生成"

# 生成其他密钥
DASHBOARD_USERNAME="supabase"
DASHBOARD_PASSWORD=$(openssl rand -base64 24 | tr -d "=+/" | cut -c1-24)
LOGFLARE_API_KEY=$(openssl rand -base64 32 | tr -d "=+/" | cut -c1-32)
LOGFLARE_SOURCE_TOKEN=$(openssl rand -base64 32 | tr -d "=+/" | cut -c1-32)

print_success "✓ 管理面板密码已生成"
print_success "✓ Logflare 密钥已生成"

# 生成 API 密钥 (使用 Node.js 和 jsonwebtoken)
print_info "正在生成 API 密钥..."

# 创建临时 Node.js 脚本来生成 JWT
cat > /tmp/generate_jwt.js << 'EOF'
const jwt = require('jsonwebtoken');

const secret = process.argv[2];
const payload = JSON.parse(process.argv[3]);

try {
    const token = jwt.sign(payload, secret, { expiresIn: '10y' });
    console.log(token);
} catch (error) {
    console.error('Error generating JWT:', error.message);
    process.exit(1);
}
EOF

# 检查是否有 jsonwebtoken 包
if ! node -e "require('jsonwebtoken')" 2>/dev/null; then
    print_warning "未找到 jsonwebtoken 包，正在尝试全局安装..."
    if command -v npm &> /dev/null; then
        npm install -g jsonwebtoken 2>/dev/null || {
            print_warning "无法安装 jsonwebtoken，将生成占位符 API 密钥"
            ANON_KEY="your_anon_key_here_please_generate_manually"
            SERVICE_ROLE_KEY="your_service_role_key_here_please_generate_manually"
        }
    else
        print_warning "未找到 npm，将生成占位符 API 密钥"
        ANON_KEY="your_anon_key_here_please_generate_manually"
        SERVICE_ROLE_KEY="your_service_role_key_here_please_generate_manually"
    fi
fi

# 生成 API 密钥的改进方法
generate_api_keys() {
    local jwt_secret="$1"
    
    # 尝试使用 Python 生成 JWT (更可靠)
    if command -v python3 &> /dev/null; then
        print_info "尝试使用 Python 生成 API 密钥..."
        
        # 创建临时 Python 脚本
        cat > /tmp/generate_jwt.py << 'EOF'
import sys
import json
import base64
import hmac
import hashlib
from datetime import datetime, timedelta

def create_jwt(payload, secret):
    # JWT Header
    header = {
        "alg": "HS256",
        "typ": "JWT"
    }
    
    # Add expiration (10 years from now)
    payload["exp"] = int((datetime.now() + timedelta(days=3650)).timestamp())
    
    # Encode header and payload
    header_encoded = base64.urlsafe_b64encode(json.dumps(header, separators=(',', ':')).encode()).decode().rstrip('=')
    payload_encoded = base64.urlsafe_b64encode(json.dumps(payload, separators=(',', ':')).encode()).decode().rstrip('=')
    
    # Create signature
    message = f"{header_encoded}.{payload_encoded}"
    signature = hmac.new(secret.encode(), message.encode(), hashlib.sha256).digest()
    signature_encoded = base64.urlsafe_b64encode(signature).decode().rstrip('=')
    
    return f"{header_encoded}.{payload_encoded}.{signature_encoded}"

if __name__ == "__main__":
    if len(sys.argv) != 3:
        sys.exit(1)
    
    secret = sys.argv[1]
    payload = json.loads(sys.argv[2])
    
    try:
        token = create_jwt(payload, secret)
        print(token)
    except Exception as e:
        sys.exit(1)
EOF

        # 生成 ANON_KEY
        local anon_payload='{"role":"anon","iss":"supabase"}'
        ANON_KEY=$(python3 /tmp/generate_jwt.py "$jwt_secret" "$anon_payload" 2>/dev/null || echo "generation_failed")
        
        # 生成 SERVICE_ROLE_KEY
        local service_payload='{"role":"service_role","iss":"supabase"}'
        SERVICE_ROLE_KEY=$(python3 /tmp/generate_jwt.py "$jwt_secret" "$service_payload" 2>/dev/null || echo "generation_failed")
        
        # 清理临时文件
        rm -f /tmp/generate_jwt.py
        
        if [ "$ANON_KEY" != "generation_failed" ] && [ "$SERVICE_ROLE_KEY" != "generation_failed" ]; then
            print_success "✓ API 密钥已通过 Python 生成"
            return 0
        fi
    fi
    
    # 如果 Python 方法失败，尝试 Node.js
    if command -v node &> /dev/null && node -e "require('jsonwebtoken')" 2>/dev/null; then
        print_info "尝试使用 Node.js 生成 API 密钥..."
        
        # 生成 ANON_KEY
        local anon_payload='{"role":"anon","iss":"supabase"}'
        ANON_KEY=$(node /tmp/generate_jwt.js "$jwt_secret" "$anon_payload" 2>/dev/null || echo "generation_failed")
        
        # 生成 SERVICE_ROLE_KEY
        local service_payload='{"role":"service_role","iss":"supabase"}'
        SERVICE_ROLE_KEY=$(node /tmp/generate_jwt.js "$jwt_secret" "$service_payload" 2>/dev/null || echo "generation_failed")
        
        if [ "$ANON_KEY" != "generation_failed" ] && [ "$SERVICE_ROLE_KEY" != "generation_failed" ]; then
            print_success "✓ API 密钥已通过 Node.js 生成"
            return 0
        fi
    fi
    
    # 如果都失败了
    print_warning "无法自动生成 API 密钥，需要手动生成"
    ANON_KEY="PLEASE_GENERATE_MANUALLY"
    SERVICE_ROLE_KEY="PLEASE_GENERATE_MANUALLY"
    return 1
}

# 调用 API 密钥生成函数
generate_api_keys "$JWT_SECRET"

# 清理临时文件
rm -f /tmp/generate_jwt.js

# 复制并替换配置文件
print_info "正在创建配置文件..."
cp "$SOURCE_FILE" "$TARGET_FILE"

# 安全替换函数 - 处理特殊字符
safe_replace() {
    local file="$1"
    local search="$2"
    local replace="$3"
    
    # 转义特殊字符
    local escaped_search=$(printf '%s\n' "$search" | sed 's/[[\.*^$()+{}|]/\\&/g')
    local escaped_replace=$(printf '%s\n' "$replace" | sed 's/[[\.*^$(){}|/]/\\&/g')
    
    # 使用 sed 进行替换
    if [[ "$OSTYPE" == "darwin"* ]]; then
        sed -i '' "s|$escaped_search|$escaped_replace|g" "$file"
    else
        sed -i "s|$escaped_search|$escaped_replace|g" "$file"
    fi
}

# 使用安全替换函数替换配置项
print_info "正在替换配置项..."

# 定义需要替换的配置项
declare -A replacements=(
    ["your-super-secret-jwt-token-with-at-least-32-characters-long"]="$JWT_SECRET"
    ["your-super-secret-and-long-postgres-password"]="$POSTGRES_PASSWORD"
    ["your-dashboard-password"]="$DASHBOARD_PASSWORD"
    ["your-secret-logflare-api-key"]="$LOGFLARE_API_KEY"
    ["your-secret-logflare-source-token"]="$LOGFLARE_SOURCE_TOKEN"
)

# 执行基本替换
for search in "${!replacements[@]}"; do
    replace="${replacements[$search]}"
    safe_replace "$TARGET_FILE" "$search" "$replace"
done

# 处理 API 密钥替换 (更安全的方法)
if [ "$ANON_KEY" != "PLEASE_GENERATE_MANUALLY" ]; then
    # 使用 grep 找到正确的行号，然后替换
    if grep -q "ANON_KEY=" "$TARGET_FILE"; then
        if [[ "$OSTYPE" == "darwin"* ]]; then
            sed -i '' "s|^ANON_KEY=.*|ANON_KEY=$ANON_KEY|" "$TARGET_FILE"
        else
            sed -i "s|^ANON_KEY=.*|ANON_KEY=$ANON_KEY|" "$TARGET_FILE"
        fi
    fi
fi

if [ "$SERVICE_ROLE_KEY" != "PLEASE_GENERATE_MANUALLY" ]; then
    # 使用 grep 找到正确的行号，然后替换
    if grep -q "SERVICE_ROLE_KEY=" "$TARGET_FILE"; then
        if [[ "$OSTYPE" == "darwin"* ]]; then
            sed -i '' "s|^SERVICE_ROLE_KEY=.*|SERVICE_ROLE_KEY=$SERVICE_ROLE_KEY|" "$TARGET_FILE"
        else
            sed -i "s|^SERVICE_ROLE_KEY=.*|SERVICE_ROLE_KEY=$SERVICE_ROLE_KEY|" "$TARGET_FILE"
        fi
    fi
fi

print_success "✓ 配置文件 '$TARGET_FILE' 已创建"

# 打印生成的密钥信息
echo
print_header
echo -e "${CYAN}生成的密钥信息：${NC}"
echo -e "${CYAN}===============${NC}"
echo
echo -e "${YELLOW}JWT 密钥:${NC}"
echo -e "${GREEN}$JWT_SECRET${NC}"
echo
echo -e "${YELLOW}数据库密码:${NC}"
echo -e "${GREEN}$POSTGRES_PASSWORD${NC}"
echo
echo -e "${YELLOW}管理面板登录:${NC}"
echo -e "用户名: ${GREEN}$DASHBOARD_USERNAME${NC}"
echo -e "密码: ${GREEN}$DASHBOARD_PASSWORD${NC}"
echo
echo -e "${YELLOW}Logflare API Key:${NC}"
echo -e "${GREEN}$LOGFLARE_API_KEY${NC}"
echo
echo -e "${YELLOW}Logflare Source Token:${NC}"
echo -e "${GREEN}$LOGFLARE_SOURCE_TOKEN${NC}"
echo

if [ "$ANON_KEY" != "PLEASE_GENERATE_MANUALLY" ]; then
    echo -e "${YELLOW}ANON Key:${NC}"
    echo -e "${GREEN}$ANON_KEY${NC}"
    echo
    echo -e "${YELLOW}SERVICE ROLE Key:${NC}"
    echo -e "${GREEN}$SERVICE_ROLE_KEY${NC}"
    echo
else
    print_warning "API 密钥需要手动生成！"
    echo -e "${YELLOW}请访问以下网址生成 API 密钥：${NC}"
    echo -e "${BLUE}https://supabase.com/docs/guides/self-hosting/docker#generate-api-keys${NC}"
    echo -e "${YELLOW}或者使用在线 JWT 生成器：${NC}"
    echo -e "${BLUE}https://jwt.io${NC}"
    echo -e "${YELLOW}使用 JWT 密钥：${NC} ${GREEN}$JWT_SECRET${NC}"
    echo -e "${YELLOW}ANON 载荷：${NC} {\"role\":\"anon\",\"iss\":\"supabase\"}"
    echo -e "${YELLOW}SERVICE 载荷：${NC} {\"role\":\"service_role\",\"iss\":\"supabase\"}"
    echo
fi

# 安全提醒
echo -e "${RED}⚠️  安全提醒：${NC}"
echo -e "${RED}===============${NC}"
echo -e "1. ${RED}请妥善保存这些密钥，它们不会再次显示${NC}"
echo -e "2. ${RED}不要将 .env 文件提交到版本控制系统${NC}"
echo -e "3. ${RED}在生产环境中请使用更强的密码策略${NC}"
echo -e "4. ${RED}定期轮换密钥以提高安全性${NC}"
echo

# 后续步骤提示
echo -e "${BLUE}后续步骤：${NC}"
echo -e "${BLUE}==========${NC}"
echo -e "1. 检查并根据需要修改 ${GREEN}$TARGET_FILE${NC} 中的其他配置项"
echo -e "2. 运行 ${GREEN}docker compose up -d${NC} 启动 Supabase"
echo -e "3. 访问 ${GREEN}http://localhost:3000${NC} 打开管理界面"
echo -e "4. 使用上面显示的用户名和密码登录"
echo

print_success "环境配置生成完成！"
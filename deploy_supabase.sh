#!/bin/bash

# Supabase Ubuntu 自动部署脚本
# 适用于 Ubuntu 20.04/22.04
# 作者: Claude
# 版本: 1.0

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # 无颜色

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 检查是否为root用户
check_root() {
    if [[ $EUID -eq 0 ]]; then
        log_error "请不要使用root用户运行此脚本，使用sudo权限的普通用户"
        exit 1
    fi
}

# 检查Ubuntu版本
check_ubuntu_version() {
    if [[ -f /etc/os-release ]]; then
        source /etc/os-release
        if [[ "$ID" != "ubuntu" ]]; then
            log_error "此脚本仅支持Ubuntu系统"
            exit 1
        fi
        log_info "检测到Ubuntu $VERSION_ID"
    else
        log_error "无法检测系统版本"
        exit 1
    fi
}

# 更新系统包
update_system() {
    log_step "更新系统包..."
    sudo apt update && sudo apt upgrade -y
    sudo apt install -y curl wget git unzip software-properties-common apt-transport-https ca-certificates gnupg lsb-release
}

# 安装Docker
install_docker() {
    log_step "安装Docker..."
    
    # 检查Docker是否已安装
    if command -v docker &> /dev/null; then
        log_info "Docker已安装，跳过安装步骤"
        return
    fi
    
    # 添加Docker官方GPG密钥
    curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg
    
    # 添加Docker仓库
    echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable" | sudo tee /etc/apt/sources.list.d/docker.list > /dev/null
    
    # 安装Docker
    sudo apt update
    sudo apt install -y docker-ce docker-ce-cli containerd.io
    
    # 将当前用户添加到docker组
    sudo usermod -aG docker $USER
    
    # 启动并启用Docker服务
    sudo systemctl start docker
    sudo systemctl enable docker
    
    log_info "Docker安装完成"
}

# 安装Docker Compose
install_docker_compose() {
    log_step "安装Docker Compose..."
    
    # 检查Docker Compose是否已安装
    if command -v docker-compose &> /dev/null; then
        log_info "Docker Compose已安装，跳过安装步骤"
        return
    fi
    
    # 获取最新版本号
    COMPOSE_VERSION=$(curl -s https://api.github.com/repos/docker/compose/releases/latest | grep 'tag_name' | cut -d\" -f4)
    
    # 下载Docker Compose
    sudo curl -L "https://github.com/docker/compose/releases/download/${COMPOSE_VERSION}/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
    
    # 添加执行权限
    sudo chmod +x /usr/local/bin/docker-compose
    
    log_info "Docker Compose安装完成，版本: $COMPOSE_VERSION"
}

# 安装Node.js和npm
install_nodejs() {
    log_step "安装Node.js..."
    
    # 检查Node.js是否已安装
    if command -v node &> /dev/null; then
        NODE_VERSION=$(node --version)
        log_info "Node.js已安装，版本: $NODE_VERSION"
        return
    fi
    
    # 安装NodeSource仓库
    curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
    
    # 安装Node.js
    sudo apt install -y nodejs
    
    log_info "Node.js安装完成，版本: $(node --version)"
}

# 克隆或更新Supabase仓库
clone_supabase() {
    log_step "准备Supabase仓库..."
    
    SUPABASE_BASE_DIR="$HOME/supabase"
    SUPABASE_DIR="$HOME/supabase/docker"
    
    if [[ -d "$SUPABASE_BASE_DIR" ]]; then
        log_info "Supabase目录已存在"

        # 询问用户如何处理
        echo "发现现有Supabase安装，请选择操作："
        echo "1) 使用现有仓库（推荐，快速）"
        echo "2) 更新现有仓库"
        echo "3) 重新下载（备份现有目录）"
        echo "4) 删除并重新下载"

        read -p "请选择 [1-4]: " choice

        case $choice in
            1)
                log_info "使用现有Supabase仓库"
                ;;
            2)
                log_info "更新现有仓库..."
                cd "$SUPABASE_BASE_DIR"
                git pull origin master || {
                    log_warn "更新失败，将使用现有版本"
                }
                ;;
            3)
                log_warn "备份现有目录并重新下载..."
                mv "$SUPABASE_BASE_DIR" "${SUPABASE_BASE_DIR}_backup_$(date +%Y%m%d_%H%M%S)"
                git clone --depth 1 https://github.com/supabase/supabase.git "$SUPABASE_BASE_DIR"
                ;;
            4)
                log_warn "删除现有目录并重新下载..."
                rm -rf "$SUPABASE_BASE_DIR"
                git clone --depth 1 https://github.com/supabase/supabase.git "$SUPABASE_BASE_DIR"
                ;;
            *)
                log_info "无效选择，使用现有仓库"
                ;;
        esac
    else
        log_info "克隆Supabase仓库..."
        git clone --depth 1 https://github.com/supabase/supabase.git "$SUPABASE_BASE_DIR"
    fi

    cd "$SUPABASE_DIR"
    log_info "Supabase仓库准备完成"
}

# 配置环境变量
configure_environment() {
    log_step "配置环境变量..."
    
    # 复制环境变量模板
    cp .env.example .env
    
    # 生成随机密码和密钥（只使用字母数字，避免特殊字符）
    JWT_SECRET=$(openssl rand -hex 32)
    POSTGRES_PASSWORD=$(openssl rand -hex 16)
    DASHBOARD_PASSWORD=$(openssl rand -hex 8)
    
    # 使用更安全的方式更新.env文件
    # 创建临时文件并逐行处理
    while IFS= read -r line; do
        if [[ "$line" == *"your-super-secret-jwt-token-with-at-least-32-characters-long"* ]]; then
            echo "${line/your-super-secret-jwt-token-with-at-least-32-characters-long/$JWT_SECRET}"
        elif [[ "$line" == *"your-super-secret-and-long-postgres-password"* ]]; then
            echo "${line/your-super-secret-and-long-postgres-password/$POSTGRES_PASSWORD}"
        elif [[ "$line" == *"this_password_is_insecure_and_should_be_updated"* ]]; then
            echo "${line/this_password_is_insecure_and_should_be_updated/$DASHBOARD_PASSWORD}"
        else
            echo "$line"
        fi
    done < .env > .env.tmp && mv .env.tmp .env
    
    # 保存密码到文件
    cat > supabase_credentials.txt << EOF
Supabase部署凭据信息
===================
PostgreSQL密码: ${POSTGRES_PASSWORD}
Dashboard密码: ${DASHBOARD_PASSWORD}
JWT密钥: ${JWT_SECRET}

访问信息:
- Supabase Studio: http://localhost:3000
- PostgreSQL: localhost:5432
- API Gateway: http://localhost:8000

请妥善保存这些凭据！
EOF
    
    log_info "环境变量配置完成，凭据已保存到 supabase_credentials.txt"
    log_warn "重要: 请记录Dashboard密码: ${DASHBOARD_PASSWORD}"
}

# 检查Docker权限
check_docker_permissions() {
    log_step "检查Docker权限..."

    # 测试Docker命令是否可用
    if docker ps &>/dev/null; then
        log_info "Docker权限正常"
        return 0
    elif sudo docker ps &>/dev/null; then
        log_warn "需要使用sudo运行Docker命令"
        return 1
    else
        log_error "Docker服务未运行或权限不足"
        return 2
    fi
}

# 启动Supabase服务
start_supabase() {
    log_step "启动Supabase服务..."

    # 检查Docker权限
    check_docker_permissions
    local docker_status=$?

    case $docker_status in
        0)
            # 直接使用docker命令
            log_info "使用普通用户权限启动服务..."
            docker-compose up -d
            ;;
        1)
            # 使用sudo
            log_info "使用sudo权限启动服务..."
            sudo docker-compose up -d
            ;;
        2)
            # Docker权限问题
            log_error "Docker权限配置有问题，尝试修复..."

            # 检查用户是否在docker组中
            if ! groups $USER | grep -q docker; then
                log_warn "用户不在docker组中，正在添加..."
                sudo usermod -aG docker $USER
                log_warn "用户已添加到docker组，但需要重新登录才能生效"
                log_warn "现在将使用sudo权限启动服务..."
                sudo docker-compose up -d
            else
                log_warn "用户已在docker组中，但可能需要重新登录"
                log_warn "尝试使用sudo权限启动服务..."
                sudo docker-compose up -d
            fi
            ;;
    esac

    log_info "Supabase服务启动完成"
}

# 等待服务启动
wait_for_services() {
    log_step "等待服务启动..."

    echo "等待所有服务启动中..."
    sleep 30

    # 检查服务状态 - 使用适当的权限
    local compose_cmd="docker-compose"
    if ! docker ps &>/dev/null && sudo docker ps &>/dev/null; then
        compose_cmd="sudo docker-compose"
    fi

    if $compose_cmd ps | grep -q "Up"; then
        log_info "服务启动成功！"
        echo "运行中的服务:"
        $compose_cmd ps --format "table {{.Name}}\t{{.State}}\t{{.Ports}}"
    else
        log_error "某些服务可能未正常启动，请检查日志"
        $compose_cmd logs
    fi
}

# 配置防火墙
configure_firewall() {
    log_step "配置防火墙..."
    
    if command -v ufw &> /dev/null; then
        sudo ufw allow 3000/tcp  # Supabase Studio
        sudo ufw allow 8000/tcp  # API Gateway
        sudo ufw allow 5432/tcp  # PostgreSQL (可选，仅在需要外部访问时)
        log_info "防火墙规则已添加"
    else
        log_warn "UFW未安装，请手动配置防火墙规则"
    fi
}

# 创建管理脚本
create_management_scripts() {
    log_step "创建管理脚本..."

    # 检测是否需要sudo
    local docker_cmd="docker-compose"
    local docker_system_cmd="docker system"
    if ! docker ps &>/dev/null && sudo docker ps &>/dev/null; then
        docker_cmd="sudo docker-compose"
        docker_system_cmd="sudo docker system"
    fi

    # 创建启动脚本
    cat > start_supabase.sh << EOF
#!/bin/bash
cd ~/supabase/docker
$docker_cmd up -d
echo "Supabase已启动"
echo "访问地址: http://localhost:3000"
EOF

    # 创建停止脚本
    cat > stop_supabase.sh << EOF
#!/bin/bash
cd ~/supabase/docker
$docker_cmd down
echo "Supabase已停止"
EOF

    # 创建状态检查脚本
    cat > status_supabase.sh << EOF
#!/bin/bash
cd ~/supabase/docker
echo "=== Supabase服务状态 ==="
$docker_cmd ps
echo ""
echo "=== 磁盘使用情况 ==="
$docker_system_cmd df
EOF

    # 创建重启脚本
    cat > restart_supabase.sh << EOF
#!/bin/bash
cd ~/supabase/docker
echo "正在重启Supabase服务..."
$docker_cmd restart
echo "Supabase已重启"
EOF

    # 创建日志查看脚本
    cat > logs_supabase.sh << EOF
#!/bin/bash
cd ~/supabase/docker
echo "=== Supabase服务日志 ==="
$docker_cmd logs
EOF

    # 添加执行权限
    chmod +x start_supabase.sh stop_supabase.sh status_supabase.sh restart_supabase.sh logs_supabase.sh

    log_info "管理脚本创建完成"
    log_info "可用脚本: start_supabase.sh, stop_supabase.sh, status_supabase.sh, restart_supabase.sh, logs_supabase.sh"
}

# 显示部署信息
show_deployment_info() {
    log_step "部署完成！"
    
    echo ""
    echo "=========================================="
    echo "  Supabase 部署成功！"
    echo "=========================================="
    echo ""
    echo "访问信息:"
    echo "- Supabase Studio: http://localhost:3000"
    echo "- API端点: http://localhost:8000"
    echo "- PostgreSQL: localhost:5432"
    echo ""
    echo "管理命令:"
    echo "- 启动服务: ./start_supabase.sh"
    echo "- 停止服务: ./stop_supabase.sh"
    echo "- 查看状态: ./status_supabase.sh"
    echo "- 查看日志: cd ~/supabase/docker && docker-compose logs"
    echo ""
    echo "重要文件:"
    echo "- 配置文件: ~/supabase/docker/.env"
    echo "- 凭据信息: ~/supabase/docker/supabase_credentials.txt"
    echo ""
    echo "注意: 如果无法访问，请检查防火墙设置和服务状态"
    echo "=========================================="
}

# 主函数
main() {
    log_info "开始Supabase自动部署..."
    
    check_root
    check_ubuntu_version
    update_system
    install_docker
    install_docker_compose
    install_nodejs
    clone_supabase
    configure_environment
    start_supabase
    wait_for_services
    configure_firewall
    create_management_scripts
    show_deployment_info
    
    log_info "部署脚本执行完成！"

    # 检查Docker权限并给出建议
    if ! docker ps &>/dev/null; then
        echo ""
        log_warn "Docker权限提示:"
        log_warn "1. 如果您刚被添加到docker组，请重新登录系统以使权限生效"
        log_warn "2. 或者运行: newgrp docker"
        log_warn "3. 或者使用sudo运行Docker命令"
        echo ""
        log_info "当前管理脚本已配置为使用适当的权限"
    fi
}

# 执行主函数
main "$@"

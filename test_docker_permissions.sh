#!/bin/bash

# Docker权限测试脚本

echo "=== Docker权限测试 ==="

echo "1. 测试普通用户权限:"
if docker ps &>/dev/null; then
    echo "   ✓ 普通用户可以运行Docker命令"
    DOCKER_CMD="docker"
    COMPOSE_CMD="docker-compose"
else
    echo "   ✗ 普通用户无法运行Docker命令"
    
    echo "2. 测试sudo权限:"
    if sudo docker ps &>/dev/null; then
        echo "   ✓ sudo权限可以运行Docker命令"
        DOCKER_CMD="sudo docker"
        COMPOSE_CMD="sudo docker-compose"
    else
        echo "   ✗ 即使sudo也无法运行Docker命令"
        echo "   请检查Docker安装和服务状态"
        exit 1
    fi
fi

echo ""
echo "推荐的命令前缀:"
echo "  Docker命令: $DOCKER_CMD"
echo "  Compose命令: $COMPOSE_CMD"

echo ""
echo "测试Docker Compose:"
if command -v docker-compose &>/dev/null; then
    echo "   ✓ docker-compose 已安装"
    if $COMPOSE_CMD --version &>/dev/null; then
        echo "   ✓ docker-compose 可以运行"
    else
        echo "   ✗ docker-compose 无法运行"
    fi
else
    echo "   ✗ docker-compose 未安装"
fi

echo ""
echo "=== 测试完成 ==="

# 🎯 Supabase Studio 问题解决方案

## 问题描述
您遇到的 Studio 错误：
```
Failed to retrieve tables
Error: An error has occurred: fetch failed

Failed to load schemas
Error: An error has occurred: fetch failed
```

## 🔍 根本原因
Studio 需要连接到多个后端服务才能正常工作，但您最初只启动了部分服务：

**缺少的关键服务：**
- ❌ `kong` - API 网关 (Studio 通过它访问所有 API)
- ❌ `rest` - REST API 服务 (提供数据访问)
- ❌ `meta` - 数据库元数据 API (提供表结构信息)
- ❌ `auth` - 认证服务 (用户管理)

## ✅ 解决方案

### 方案1: 使用完整的 Docker Compose (推荐)
```bash
cd /home/<USER>/supabase/docker
docker-compose up -d
```

### 方案2: 使用模块管理器启动完整服务
```bash
cd /home/<USER>/otherWS/supabase_deploy
./supabase_manager.sh --deploy studio,kong,rest,meta,auth,db,analytics,vector
```

### 方案3: 使用预定义场景
```bash
./supabase_manager.sh --scenario SCENARIO_DEVELOPMENT
```

## 🎯 当前状态验证

### 运行中的服务
```
✅ supabase-studio      - Web 界面 (端口 3000)
✅ supabase-kong        - API 网关 (端口 8000)  
✅ supabase-rest        - REST API
✅ supabase-meta        - 数据库元数据 API
✅ supabase-auth        - 认证服务
✅ supabase-db          - PostgreSQL 数据库
✅ supabase-analytics   - 分析服务 (端口 4000)
✅ supabase-vector      - 向量数据库
✅ supabase-storage     - 文件存储
✅ supabase-realtime    - 实时功能
✅ supabase-edge-functions - Edge 函数
✅ supabase-imgproxy    - 图片处理
✅ supabase-pooler      - 连接池
```

## 🔧 验证修复

### 1. 检查服务状态
```bash
docker ps --format "table {{.Names}}\t{{.Status}}"
```

### 2. 访问 Studio
打开浏览器访问: http://localhost:3000

### 3. 测试 API 连接
```bash
# 测试 REST API
curl http://localhost:8000/rest/v1/ -H "apikey: YOUR_ANON_KEY"

# 测试 Meta API  
curl http://localhost:8080

# 测试 Kong 网关
curl http://localhost:8000
```

## 🎯 Studio 依赖关系图

```
Studio (Web界面)
    ↓
Kong (API网关) :8000
    ↓
┌─── REST API :3000 ────→ Database :5432
├─── Meta API :8080 ────→ Database :5432  
├─── Auth API ──────────→ Database :5432
└─── Storage API :5000 ─→ Database :5432
```

## 📝 重要说明

### Studio 必需的最小服务组合
```bash
# 最小可用配置
./supabase_manager.sh --deploy studio,kong,rest,meta,db

# 推荐配置 (包含认证)
./supabase_manager.sh --deploy studio,kong,rest,meta,auth,db

# 完整配置 (所有功能)
docker-compose up -d
```

### 常见错误和解决方案

**错误1: "Failed to retrieve tables"**
- 原因: 缺少 Meta API 服务
- 解决: 确保 `supabase-meta` 容器运行

**错误2: "Failed to load schemas"**  
- 原因: 缺少 REST API 或 Kong 网关
- 解决: 确保 `supabase-rest` 和 `supabase-kong` 运行

**错误3: "fetch failed"**
- 原因: 网络连接问题或服务未完全启动
- 解决: 等待所有服务健康检查通过

## 🚀 预防措施

### 1. 使用预定义场景
```bash
# 查看可用场景
./supabase_manager.sh --scenarios

# 使用开发场景 (包含 Studio)
./supabase_manager.sh --scenario SCENARIO_DEVELOPMENT
```

### 2. 定期检查服务状态
```bash
./supabase_manager.sh --status
```

### 3. 使用诊断工具
```bash
# 运行 Studio 问题诊断
./fix_studio_issues.sh

# 检查配置一致性
./check_config_consistency.sh
```

## 🎉 问题已解决！

现在您的 Supabase Studio 应该可以正常工作了：

1. ✅ **所有必需服务已启动**
2. ✅ **API 连接已建立** 
3. ✅ **数据库连接正常**
4. ✅ **Studio 可以访问表和架构**

**访问链接:**
- **Supabase Studio**: http://localhost:3000
- **API 文档**: http://localhost:8000/rest/v1/
- **分析面板**: http://localhost:4000

如果仍有问题，请刷新浏览器或运行诊断脚本 `./fix_studio_issues.sh`。
